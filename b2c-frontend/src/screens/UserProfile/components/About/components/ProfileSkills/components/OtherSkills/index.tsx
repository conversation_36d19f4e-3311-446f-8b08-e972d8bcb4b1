import type React from 'react';
import { ActivityIndicator, TouchableOpacity, View } from 'react-native';
import { Text } from 'react-native';
import type { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import ChipInput from '@/src/components/ChipInput';
import NotFound from '@/src/components/NotFound';
import type { BottomTabNavigationI, RootDrawerParamListI } from '@/src/navigation/types';
import type { OtherSkillsPropsI } from './types';
import { useOtherSkills } from './useHook';

const OtherSkills: React.FC<OtherSkillsPropsI> = ({ isUserProfile, profileId }) => {
  const { localSkills, loading, count } = useOtherSkills(profileId);
  const bottomTabNavigation = useNavigation<BottomTabNavigationI>();
  const drawerNavigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();

  if (loading) {
    return <ActivityIndicator size="small" className="pt-14 pb-6" />;
  }

  return (
    <View className="px-2 pt-2 pb-8">
      <ChipInput
        title="Skills"
        placeholder="Add a skill"
        chips={localSkills}
        disabled={true}
        removable={false}
        showBorder={false}
        showTitle={false}
      />
      {count > 3 && (
        <TouchableOpacity
          onPress={() =>
            isUserProfile
              ? drawerNavigation.navigate('ProfileStack', {
                  screen: 'EditSkillsList',
                  params: { category: 'otherSkills', profileId: profileId, editable: false },
                })
              : bottomTabNavigation.navigate('HomeStack', {
                  screen: 'EditSkillsList',
                  params: { category: 'otherSkills', profileId: profileId, editable: false },
                })
          }
        >
          <Text className="text-[#448600] text-base font-medium">{`View all ${count} Other Skills`}</Text>
        </TouchableOpacity>
      )}
      {localSkills.length === 0 && (
        <NotFound
          className="pb-4"
          titleClassName="font-normal"
          imageStyle={{ height: 120, width: 120 }}
          fullScreen={false}
        />
      )}
    </View>
  );
};

export default OtherSkills;
