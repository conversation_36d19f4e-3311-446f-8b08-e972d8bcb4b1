import type React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { Text } from 'react-native';
import type { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import ChipInput from '@/src/components/ChipInput';
import NotFound from '@/src/components/NotFound';
import type { BottomTabNavigationI, RootDrawerParamListI } from '@/src/navigation/types';
import type { MaritimeSkillsPropsI } from './types';
import { useMaritimeSkills } from './useHook';

const MaritimeSkills: React.FC<MaritimeSkillsPropsI> = ({ isUserProfile, profileId }) => {
  const { localSkills, count } = useMaritimeSkills();
  const bottomTabNavigation = useNavigation<BottomTabNavigationI>();
  const drawerNavigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();

  return (
    <View className="px-2 pt-2 pb-8">
      <ChipInput
        title="Skills"
        placeholder="Add a skill"
        chips={localSkills}
        disabled={true}
        removable={false}
        showBorder={false}
        showTitle={false}
      />
      {count > 3 && (
        <TouchableOpacity
          onPress={() =>
            isUserProfile
              ? drawerNavigation.navigate('ProfileStack', {
                  screen: 'EditSkillsList',
                  params: { category: 'maritimeSkills', profileId: profileId, editable: false },
                })
              : bottomTabNavigation.navigate('HomeStack', {
                  screen: 'EditSkillsList',
                  params: { category: 'maritimeSkills', profileId: profileId, editable: false },
                })
          }
        >
          <Text className="text-[#448600] text-base font-medium">{`View all ${count} Maritime Skills`}</Text>
        </TouchableOpacity>
      )}
      {count === 0 && (
        <NotFound
          className="pt-10 pb-4"
          titleClassName="font-normal"
          imageStyle={{ height: 120, width: 120 }}
          fullScreen={false}
        />
      )}
    </View>
  );
};

export default MaritimeSkills;
