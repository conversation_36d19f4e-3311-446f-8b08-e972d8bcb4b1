import React from 'react';
import { View, FlatList } from 'react-native';
import ShimmerBox from '@/src/components/Shimmer';

const ForumPostSkeleton = () => (
  <View className="mx-3 my-4">
    <View className="flex-row items-center mb-2">
      <ShimmerBox variant="sphere" width={40} height={40} />
      <View className="flex-1 ml-3">
        <ShimmerBox width={'60%'} height={18} borderRadius={4} className="mb-1" />
        <ShimmerBox width={'40%'} height={14} borderRadius={4} />
      </View>
    </View>
    <ShimmerBox width={'80%'} height={20} borderRadius={4} className="mb-2" />
    <ShimmerBox width={'100%'} height={16} borderRadius={4} className="mb-1" />
    <ShimmerBox width={'90%'} height={16} borderRadius={4} className="mb-1" />
    <ShimmerBox width={'70%'} height={16} borderRadius={4} />
    <View className="flex-row items-center justify-between mt-2">
      <ShimmerBox width={60} height={28} borderRadius={14} />
      <ShimmerBox width={60} height={28} borderRadius={14} />
      <ShimmerBox width={60} height={28} borderRadius={14} />
    </View>
  </View>
);

const AnswerSkeleton = () => (
  <View className="bg-white overflow-hidden py-2 border-b border-gray-200 mb-3 rounded-lg px-4">
    <View className="flex-row items-center mb-2">
      <ShimmerBox variant="sphere" width={28} height={28} />
      <ShimmerBox width={80} height={14} borderRadius={4} className="ml-2" />
    </View>
    <ShimmerBox width={'90%'} height={16} borderRadius={4} className="mb-1 ml-10" />
    <ShimmerBox width={'80%'} height={16} borderRadius={4} className="mb-1 ml-10" />
    <View className="flex-row items-center gap-4 mt-2 ml-10">
      <ShimmerBox width={40} height={20} borderRadius={10} />
      <ShimmerBox width={40} height={20} borderRadius={10} />
      <ShimmerBox width={40} height={20} borderRadius={10} />
    </View>
  </View>
);

const AnswerListSkeleton = () => (
  <View className="flex-1 bg-white">
    <FlatList
      data={Array.from({ length: 3 })}
      keyExtractor={(_, idx) => idx.toString()}
      renderItem={() => <AnswerSkeleton />}
      ListHeaderComponent={<ForumPostSkeleton />}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{
        flexGrow: 1,
        backgroundColor: 'white',
        paddingBottom: 20,
      }}
    />
  </View>
);

export default AnswerListSkeleton;
