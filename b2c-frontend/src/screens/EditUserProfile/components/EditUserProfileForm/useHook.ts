import { useState, useEffect } from 'react';
import { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import type { Image } from 'react-native-image-crop-picker';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectSelectionByKey } from '@/src/redux/selectors/search';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { updateDescription } from '@/src/redux/slices/about/aboutSlice';
import { clearSelection } from '@/src/redux/slices/entitysearch/searchSlice';
import { updateUserProfile } from '@/src/redux/slices/user/userSlice';
import type { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import { uploadFileWithPresignedUrl } from '@/src/utilities/upload/upload';
import type { RootDrawerParamListI } from '@/src/navigation/types';
import { editDeleteAvatarStorageAPI } from '@/src/networks/profile/avatar';
import { editUserProfileAPI } from '@/src/networks/profile/userProfile';
import { fetchPresignedUrlAPI } from '@/src/networks/storage/presignedUrl';
import type {
  EditUserProfileFormDataI,
  UseEditUserProfileFormI,
  PresignedUrlResponseI,
} from './types';

const useEditUserProfile = (): UseEditUserProfileFormI => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [avatarFile, setAvatarFile] = useState<Image | null>(null);
  const [isAvatarDeleted, setIsAvatarDeleted] = useState(false);
  const navigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();
  const dispatch = useDispatch<AppDispatch>();
  const currentUser = useSelector(selectCurrentUser);
  const entitySelection = useSelector(selectSelectionByKey('entity'));
  const designationSelection = useSelector(selectSelectionByKey('designation'));
  const [loading, setLoading] = useState(false);

  const methods = useForm<EditUserProfileFormDataI>({
    mode: 'onChange',
    defaultValues: {
      name: currentUser.fullName || '',
      designation: designationSelection || currentUser.designation,
      entity: entitySelection || currentUser.organisation,
      description: currentUser.description || '',
      avatar: currentUser.avatar || null,
    },
  });

  useEffect(() => {
    if (entitySelection) {
      methods.setValue('entity', entitySelection, { shouldDirty: true });
    }
  }, [entitySelection, methods]);

  useEffect(() => {
    if (designationSelection) {
      methods.setValue('designation', designationSelection, { shouldDirty: true });
    }
  }, [designationSelection, methods]);

  const avatarChanged = avatarFile !== null || isAvatarDeleted;
  const hasChanges = methods.formState.isDirty || avatarChanged;

  const isGoogleAvatar = (avatarUrl: string | null): boolean => {
    return (
      avatarUrl?.includes('googleusercontent.com') ||
      avatarUrl?.includes('lh3.googleusercontent.com') ||
      false
    );
  };

  const handleAvatarChange = async (image: Image) => {
    try {
      setLoading(true);
      if (currentUser.avatar && !isGoogleAvatar(currentUser.avatar)) {
        await editDeleteAvatarStorageAPI({ fileUrl: currentUser.avatar });
      }
      setAvatarFile(image);
      setIsAvatarDeleted(false);
      methods.setValue('avatar', image, { shouldDirty: true });
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Error Editing Profile Avatar.',
        description: 'Try after Some time',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarDelete = async () => {
    try {
      setLoading(true);
      if (currentUser.avatar && !isGoogleAvatar(currentUser.avatar)) {
        await editDeleteAvatarStorageAPI({ fileUrl: currentUser.avatar });
      }
      setAvatarFile(null);
      setIsAvatarDeleted(true);
      methods.setValue('avatar', null, { shouldDirty: true });
    } catch (error) {
      showToast({
        type: 'error',
        message: 'Error Deleting Profile Avatar.',
        description: 'Try after Some time',
      });
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: EditUserProfileFormDataI) => {
    try {
      setIsSubmitting(true);
      const finalDesignation = designationSelection ?? data.designation ?? currentUser.designation;
      const finalEntity = entitySelection ?? data.entity ?? currentUser.organisation;
      const finalName = data.name ?? currentUser.fullName;
      const finalDescription = data.description ?? currentUser.description;

      let avatarUrl = currentUser.avatar;
      if (isAvatarDeleted) {
        avatarUrl = null;
      } else if (avatarFile && !avatarFile.path?.startsWith('http')) {
        const extension = avatarFile.mime?.split('/')[1] || 'jpeg';
        const response = await fetchPresignedUrlAPI([extension], 'POST');
        if (Array.isArray(response) && response.length > 0) {
          const presignedData = response[0] as PresignedUrlResponseI;
          const fileToUpload = {
            uri: avatarFile.path,
            type: avatarFile.mime,
            filename: `avatar.${extension}`,
          };
          await uploadFileWithPresignedUrl(fileToUpload, presignedData.uploadUrl);
          avatarUrl = presignedData.accessUrl;
        }
      }

      const payload = {
        name: finalName,
        description: finalDescription,
        designation: finalDesignation && {
          id: finalDesignation.id,
          dataType: finalDesignation.dataType,
        },
        entity: finalEntity && {
          id: finalEntity.id,
          dataType: finalEntity.dataType,
        },
        avatar: avatarUrl,
      };

      await editUserProfileAPI(payload);
      dispatch(
        updateUserProfile({
          name: finalName,
          designation: finalDesignation,
          entity: finalEntity,
          avatar: avatarUrl,
        }),
      );
      dispatch(updateDescription(finalDescription));
      dispatch(clearSelection('designation'));
      dispatch(clearSelection('entity'));
      showToast({
        message: 'Success',
        description: 'Profile updated successfully',
        type: 'success',
      });
      methods.reset(methods.getValues());
      navigation.goBack();
    } catch (error) {
      handleError(error, {
        handle4xxError: () => {
          showToast({
            message: 'Failed to update profile',
            description: 'Please check your input and try again.',
            type: 'error',
          });
        },
        handle5xxError: () => {
          showToast({
            message: 'Server Error',
            description: 'Please try again later.',
            type: 'error',
          });
        },
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
    handleAvatarChange,
    handleAvatarDelete,
    avatarFile,
    isAvatarDeleted,
    loading,
    hasChanges,
  };
};

export default useEditUserProfile;
