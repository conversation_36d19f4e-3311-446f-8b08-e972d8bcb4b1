import { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import { RootDrawerParamListI } from '@/src/navigation/types';
import EditUserProfileForm from './components/EditUserProfileForm';

const EditUserProfile = () => {
  const navigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();

  return (
    <SafeArea>
      <EditUserProfileForm
        onBack={() =>
          navigation.reset({
            index: 0,
            routes: [{ name: 'ProfileStack', params: { screen: 'UserSettings' } }],
          })
        }
      />
    </SafeArea>
  );
};

export default EditUserProfile;
