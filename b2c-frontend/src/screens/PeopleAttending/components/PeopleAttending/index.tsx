import { View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import BackButton from '@/src/components/BackButton';
import UsersList from '@/src/components/UsersList';
import { ListItem } from '@/src/components/UsersList/types';
import { NearbyStackParamListI } from '@/src/navigation/types';
import { PeopleAttendingPropsI } from './types';
import { usePeopleAttending } from './useHook';

const PeopleAttending = ({ onBack, announcementId }: PeopleAttendingPropsI) => {
  const { people, loadMorePeople, loading, loadingMore, hasMore, refreshPeople } =
    usePeopleAttending(announcementId);
  const navigation = useNavigation<StackNavigationProp<NearbyStackParamListI>>();
  const onUserPress = (item: ListItem) => {
    navigation.navigate('OtherUserProfile', {
      profileId: item.Profile.id,
      fromTabPress: false,
    });
  };

  return (
    <View>
      <BackButton onBack={onBack} label="Attendees" />
      <UsersList
        data={people as unknown as ListItem[]}
        onLoadMore={loadMorePeople}
        loading={loading}
        onRefresh={refreshPeople}
        onPress={onUserPress}
      />
    </View>
  );
};

export default PeopleAttending;
