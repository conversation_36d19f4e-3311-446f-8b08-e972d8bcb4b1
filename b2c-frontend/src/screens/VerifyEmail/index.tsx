/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { RouteProp, useRoute } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import { AppStackParamListI } from '@/src/navigation/types';
import { VerifyEmailForm } from './components/VerifyEmailForm';

type VerifyAccountRouteProp = RouteProp<AppStackParamListI, 'VerifyEmail'>;

const VerifyEmail = () => {
  const route = useRoute<VerifyAccountRouteProp>();
  const email = route.params?.email;
  const profileId = route.params?.profileId;
  return (
    <SafeArea>
      <VerifyEmailForm email={email} profileId={profileId} />
    </SafeArea>
  );
};

export default VerifyEmail;
