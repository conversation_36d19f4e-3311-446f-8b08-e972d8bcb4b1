import { useState, useEffect } from 'react';
import { Pressable, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import CustomModal from '@/src/components/Modal';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { formatSocialTime } from '@/src/utilities/datetime';
import type { BottomTabNavigationI } from '@/src/navigation/types';
import { useSocket } from '@/src/context/providers/SocketProvider';
import { deleteSpecificProfileChat } from '@/src/networks/chat/individual';
import type { ChatItemProps } from './types';

const ChatItem = ({ item, onDelete }: ChatItemProps) => {
  const navigation = useNavigation<BottomTabNavigationI>();
  const currentUser = useSelector(selectCurrentUser);
  const { subscribeToMessages } = useSocket();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [hasNewMessage, setHasNewMessage] = useState(false);
  const [lastMessage, setLastMessage] = useState<any>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [userStatus, setUserStatus] = useState<'online' | 'offline'>('offline');
  const [unreadCount, setUnreadCount] = useState(item.unreadCount || 0);

  const handleIndividualChat = (data: any) => {
    const currentUserId = currentUser.profileId;
    const isReceivedMessage = data.senderId !== currentUserId;
    const chatConditions = [
      data.senderId === item.senderId && data.recieverId === currentUserId,
      data.senderId === currentUserId && data.recieverId === item.senderId,
      data.senderId === item.recieverId && data.recieverId === currentUserId,
      data.senderId === currentUserId && data.recieverId === item.recieverId,
    ];
    const isThisChat = chatConditions.some((condition) => condition);
    if (isThisChat) {
      setLastMessage(data);
      if (isReceivedMessage) {
        setHasNewMessage(true);
        setUnreadCount((prev) => prev + 1);
      }
    }
  };

  const handleTypingStart = (data: any) => {
    const currentUserId = currentUser.profileId;
    const otherUserId = item.senderId === currentUserId ? item.recieverId : item.senderId;
    const isFromThisChat = data.senderId === otherUserId;
    if (isFromThisChat && data.senderId !== currentUserId) {
      setIsTyping(true);
    }
  };

  const handleTypingStop = (data: any) => {
    const currentUserId = currentUser.profileId;
    const otherUserId = item.senderId === currentUserId ? item.recieverId : item.senderId;
    const isFromThisChat = data.senderId === otherUserId;
    if (isFromThisChat && data.senderId !== currentUserId) {
      setIsTyping(false);
    }
  };

  const handleUserStatus = (data: any) => {
    const currentUserId = currentUser.profileId;
    const otherUserId = item.senderId === currentUserId ? item.recieverId : item.senderId;
    if (data.profileId === otherUserId) {
      setUserStatus(data.status);
    }
  };

  const handleSocketMessage = (type: string, data: any, rawMessage: any) => {
    switch (type) {
      case 'individual':
      case 'individual-chat':
        handleIndividualChat(data);
        break;
      case 'typing-start':
        handleTypingStart(data);
        break;
      case 'typing-stop':
        handleTypingStop(data);
        break;
      case 'user-status':
        handleUserStatus(data);
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    if (!subscribeToMessages) return;
    const messageTypes = [
      'individual',
      'individual-chat',
      'typing-start',
      'typing-stop',
      'user-status',
    ];
    const unsubscribe = subscribeToMessages(messageTypes, (type, data, rawMessage) => {
      handleSocketMessage(type, data, rawMessage);
    });
    return () => {
      unsubscribe();
    };
  }, [subscribeToMessages]);

  const onPress = () => {
    const currentUserId = currentUser.profileId;
    const profileId = item.senderId === currentUserId ? item.recieverId : item.senderId;
    setHasNewMessage(false);
    setUnreadCount(0);
    navigation.navigate('Chat', {
      id: profileId,
    });
  };

  const onLongPress = () => {
    setIsModalVisible(true);
  };

  const handleDeleteChat = async () => {
    const currentUserId = currentUser.profileId;
    const profileId = item.senderId === currentUserId ? item.recieverId : item.senderId;
    setIsDeleting(true);
    try {
      await deleteSpecificProfileChat(profileId);
      setIsModalVisible(false);
      onDelete(item.id.$oid);
    } catch (error: any) {
      console.error('ERROR: Error deleting chat', {
        error,
        timestamp: new Date().toISOString(),
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const getLastMessageText = () => {
    if (isTyping) return 'Typing...';
    const messageToShow = lastMessage || item;
    if (messageToShow.content?.text) return messageToShow.content.text;
    if (messageToShow.content?.media?.length > 0)
      return `${messageToShow.content.media.length} media file(s)`;
    return 'No message';
  };

  return (
    <>
      <Pressable onPress={onPress} onLongPress={onLongPress}>
        <View className="flex-row items-center justify-between p-3">
          <View className="relative">
            <UserAvatar
              avatarUri={item.profile?.avatar!}
              width={50}
              height={50}
              name={item.profile?.name || 'Unknown'}
            />
            <View
              className={`absolute bottom-0 right-0 w-3 h-3 rounded-full border-2 border-white ${
                userStatus === 'online' ? 'bg-green-500' : 'bg-gray-400'
              }`}
            />
            {hasNewMessage && (
              <View className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                <View className="w-2 h-2 bg-white rounded-full" />
              </View>
            )}
            {unreadCount > 0 && (
              <View className="absolute -top-2 -right-2 min-w-5 h-5 bg-green-500 rounded-full px-1 flex items-center justify-center">
                <Text className="text-white text-xs font-bold">{unreadCount}</Text>
              </View>
            )}
          </View>
          <View className="flex-1 px-3">
            <View className="flex-row items-center gap-2">
              <Text className="text-base font-semibold text-black">
                {item.profile?.name || 'Unknown User'}
              </Text>
            </View>
            <Text
              numberOfLines={1}
              ellipsizeMode="tail"
              className={`text-sm mt-0.5 w-full ${isTyping ? 'text-green-800 italic' : 'text-gray-500'}`}
            >
              {getLastMessageText()}
            </Text>
          </View>
          <View className="flex-col items-end justify-center">
            <Text className="text-xs text-gray-400">
              {formatSocialTime(new Date(item.createdAt).getTime())}
            </Text>
            {unreadCount > 0 && (
              <View className="mt-1 bg-green-500 rounded-full px-2 py-0.5 items-center justify-center min-w-[20px]">
                <Text className="text-white text-xs font-bold">{unreadCount}</Text>
              </View>
            )}
          </View>
        </View>
      </Pressable>
      <CustomModal
        isVisible={isModalVisible}
        title="Delete Chat"
        description="Are you sure you want to delete this chat? This action cannot be undone."
        cancelText="Cancel"
        confirmText="Delete"
        confirmButtonVariant="danger"
        onConfirm={handleDeleteChat}
        onCancel={() => setIsModalVisible(false)}
        isConfirming={isDeleting}
      />
    </>
  );
};

export default ChatItem;
