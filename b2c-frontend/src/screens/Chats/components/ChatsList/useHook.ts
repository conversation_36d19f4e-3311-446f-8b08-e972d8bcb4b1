import { useState, useEffect, useRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { useSocket } from '@/src/context/providers/SocketProvider';
import { findAllIndividualChats } from '@/src/networks/chat/individual';
import { fetchProfileAPI } from '@/src/networks/profile/userProfile';
import type { ChatListItem, ProfileData } from '../ChatItem/types';
import type { ChatsListState } from './types';

const ASYNC_STORAGE_TOTAL_UNREAD_COUNT_KEY = '@totalUnreadCount';

export const useChatsListHook = () => {
  const [state, setState] = useState<ChatsListState>({
    chats: [],
    loading: true,
    error: null,
    refreshing: false,
    loadingMore: false,
    hasMore: true,
    currentPage: 0,
    totalChats: 0,
  });

  const currentUser = useSelector(selectCurrentUser);
  const PAGE_SIZE = 10;
  const loadingRef = useRef(false);
  const mountedRef = useRef(true);
  const { setTotalUnreadCount } = useSocket();

  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const loadChats = async (isRefresh = true, initialLoad = false) => {
    if (loadingRef.current && !isRefresh && !initialLoad) return;
    if (!mountedRef.current) return;

    try {
      loadingRef.current = true;

      if (initialLoad) {
        const cachedTotalUnread = await AsyncStorage.getItem(ASYNC_STORAGE_TOTAL_UNREAD_COUNT_KEY);

        if (cachedTotalUnread) {
          setTotalUnreadCount(Number(cachedTotalUnread));
        }
      }

      if (isRefresh) {
        setState((prev) => ({ ...prev, refreshing: true, error: null }));
      } else if (state.loadingMore) {
        return;
      } else if (!state.loading && !initialLoad) {
        setState((prev) => ({ ...prev, loadingMore: true }));
      }

      const page = isRefresh ? 0 : state.currentPage + 1;
      const result = await findAllIndividualChats({
        page,
        pageSize: PAGE_SIZE,
      });

      console.log(result);

      if (!mountedRef.current) return;

      const chatsWithProfiles = await Promise.all(
        result.data.map(async (chat) => {
          const profileId =
            chat.senderId === currentUser.profileId ? chat.recieverId : chat.senderId;
          const profile = await fetchProfileAPI(profileId);
          return {
            ...chat,
            id: { $oid: chat.id.$oid },
            profile,
          } as ChatListItem;
        }),
      );

      if (!mountedRef.current) return;

      await AsyncStorage.setItem(ASYNC_STORAGE_TOTAL_UNREAD_COUNT_KEY, String(result.totalUnread));

      setTotalUnreadCount(result.totalUnread);

      setState((prev) => {
        const existingIds = new Set(prev.chats.map((chat) => chat.id.$oid));
        const newChats = chatsWithProfiles.filter((chat) => !existingIds.has(chat.id.$oid));

        return {
          ...prev,
          chats: isRefresh ? chatsWithProfiles : [...prev.chats, ...newChats],
          currentPage: page,
          totalChats: result.total,
          hasMore: result.hasMore,
          loading: false,
          refreshing: false,
          loadingMore: false,
          error: null,
        };
      });
    } catch (err) {
      if (!mountedRef.current) return;
      setState((prev) => ({
        ...prev,
        error: 'Failed to load chats',
        loading: false,
        refreshing: false,
        loadingMore: false,
      }));
    } finally {
      loadingRef.current = false;
    }
  };

  const removeChat = (chatId: string) => {
    setState((prev) => ({
      ...prev,
      chats: prev.chats.filter((chat) => chat.id.$oid !== chatId),
      totalChats: prev.totalChats - 1,
    }));
  };

  const handleRefresh = () => {
    if (!loadingRef.current) {
      setState((prev) => ({ ...prev, currentPage: 0, hasMore: true }));
      loadChats(true);
    }
  };

  const handleLoadMore = () => {
    if (!state.loadingMore && state.hasMore && !loadingRef.current) {
      loadChats(false);
    }
  };

  useEffect(() => {
    loadChats(true, true);
  }, []);

  return {
    ...state,
    handleRefresh,
    handleLoadMore,
    removeChat,
  };
};
