import { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import SafeArea from '@/src/components/SafeArea';
import { RootDrawerParamListI } from '@/src/navigation/types';
import BlockedUserProfilesList from './components/BlockedProfileList';

const BlockedProfiles = () => {
  const navigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();

  return (
    <SafeArea>
      <BlockedUserProfilesList
        onBack={() =>
          navigation.reset({
            index: 0,
            routes: [{ name: 'ProfileStack', params: { screen: 'UserSettings' } }],
          })
        }
      />
    </SafeArea>
  );
};

export default BlockedProfiles;
