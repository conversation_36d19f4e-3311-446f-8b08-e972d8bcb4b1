import { Text, View } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import RadioButton from '@/src/components/RadioButton';
import ToggleSwitch from '@/src/components/Toggle';
import {
  selectQuestionType,
  selectQuestionIsAnonymous,
  selectQuestionIsLive,
} from '@/src/redux/selectors/question';
import {
  setQuestionType,
  setIsAnonymous,
  setIsLive,
} from '@/src/redux/slices/question/questionSlice';
import type { AppDispatch } from '@/src/redux/store';
import QnaFields from '../QnaFields';
import TroubleshootingFields from '../TroubleshootingFields';

export const validateAskQuestionForm = (
  state: any,
): { isValid: boolean; errors: Record<string, string> } => {
  const errors: Record<string, string> = {};
  let isValid = true;

  const questionType = state.question.formData.type;
  const isGeneral = state.question.formData.isGeneral;
  const topicsSelection = state.search.multipleSelections.topic;
  const departmentSelection = state.search.selections.department;
  const equipmentCategorySelection = state.search.selections.equipmentCategory;
  const equipmentManufacturerSelection = state.search.selections.equipmentManufacturer;
  const equipmentModelSelection = state.search.selections.equipmentModel;

  const MAX_TOPICS = 3;

  if (questionType === 'NORMAL') {
    if (!topicsSelection || topicsSelection.length === 0) {
      errors.topics = 'Please select at least one topic for your question';
      isValid = false;
    } else if (topicsSelection.length > MAX_TOPICS) {
      errors.topics = `Maximum topic limit reached. You can only select up to ${MAX_TOPICS} topics`;
      isValid = false;
    }
    if (!isGeneral && !departmentSelection) {
      errors.department = 'Please select a department type';
      isValid = false;
    }
  } else {
    if (!equipmentCategorySelection) {
      errors.equipmentCategory = 'Please select an equipment category';
      isValid = false;
    }
    if (!equipmentManufacturerSelection) {
      errors.equipmentManufacturer = 'Please select an equipment manufacturer';
      isValid = false;
    }
    if (!equipmentModelSelection) {
      errors.equipmentModel = 'Please select an equipment model';
      isValid = false;
    }
    if (!isGeneral && !departmentSelection) {
      errors.department = 'Please select a department type';
      isValid = false;
    }
  }

  return { isValid, errors };
};

interface AskQuestionFormProps {
  formErrors: Record<string, string>;
}

const AskQuestionForm = ({ formErrors }: AskQuestionFormProps) => {
  const dispatch = useDispatch<AppDispatch>();

  const questionType = useSelector(selectQuestionType);
  const isAnonymous = useSelector(selectQuestionIsAnonymous);
  const isLive = useSelector(selectQuestionIsLive);

  const isQnA = questionType === 'NORMAL';

  const handleTypeChange = (type: 'qna' | 'troubleshoot') => {
    dispatch(setQuestionType(type === 'qna' ? 'NORMAL' : 'TROUBLESHOOT'));
  };

  const handleAnonymousToggle = () => {
    dispatch(setIsAnonymous(!isAnonymous));
  };

  const handleLiveToggle = () => {
    dispatch(setIsLive(!isLive));
  };

  return (
    <View className="mt-6 flex-1 gap-5 px-2">
      <Text className="text-xl font-medium">Ask Question</Text>
      <View className="gap-3">
        <Text className="text-sm leading-4">Post type</Text>
        <View className="flex-row justify-between gap-4">
          <View className="flex-1">
            <RadioButton
              selected={questionType === 'NORMAL'}
              onPress={() => handleTypeChange('qna')}
              label="QnA"
            />
          </View>
          <View className="flex-1">
            <RadioButton
              selected={questionType === 'TROUBLESHOOT'}
              onPress={() => handleTypeChange('troubleshoot')}
              label="Troubleshooting"
            />
          </View>
        </View>
      </View>

      {isQnA ? <QnaFields errors={formErrors} /> : <TroubleshootingFields errors={formErrors} />}

      <View className="gap-5">
        <ToggleSwitch
          label="Make anonymous"
          enabled={isAnonymous}
          onToggle={handleAnonymousToggle}
        />
        <ToggleSwitch label="Live Mode" enabled={isLive} onToggle={handleLiveToggle} />
      </View>
    </View>
  );
};

export default AskQuestionForm;
