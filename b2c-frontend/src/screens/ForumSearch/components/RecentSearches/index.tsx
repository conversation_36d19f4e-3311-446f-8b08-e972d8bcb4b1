/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import React, { useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { useSelector } from 'react-redux';
import type { ForumSearchItemI } from '@/src/redux/slices/forumsearch/types';
import type { RootState } from '@/src/redux/store';
import Anticlockwise from '@/src/assets/svgs/AntiClockwise';
import { ForumSearchCategory } from '../SearchBox/types';
import type { RecentSearchesProps } from './types';

const RecentSearches: React.FC<RecentSearchesProps> = ({
  setSearchData,
  setShowRecent,
  setActiveTab,
  setLoading,
  setLastSearchQuery,
  debouncedSearch,
  category,
}) => {
  const recentSearches = useSelector(
    (state: RootState) =>
      state.forumsearch?.recentSearches?.filter(
        (item: ForumSearchItemI) => item.category === category,
      ) || [],
  );
  const [error, setError] = useState<Error | null>(null);

  const triggerErrorBoundary = (error: Error) => {
    setError(error);
  };

  if (error) {
    throw error;
  }

  const onPress = async (item: { category: string; searchText: string }) => {
    try {
      setSearchData(item.searchText);
      setActiveTab(item.category as ForumSearchCategory);
      setShowRecent(false);
      setLoading(true);
      setLastSearchQuery(item.searchText);
      if (debouncedSearch) {
        await debouncedSearch(item.searchText, item.category as ForumSearchCategory, false);
      }
    } catch (error) {
      const errorMessage = `Failed to perform recent search: ${error instanceof Error ? error.message : 'Unknown error'}`;
      triggerErrorBoundary(new Error(errorMessage));
    }
  };

  return (
    <View className="flex-1">
      <FlatList
        data={recentSearches}
        keyExtractor={(item, index) => `${item.searchText}-${index}`}
        renderItem={({ item }) => (
          <Pressable
            onPress={() => onPress(item)}
            className="flex-row items-center py-3 px-4 mt-2"
            android_ripple={{ color: 'transparent' }}
          >
            <View className="items-center justify-center mr-3">
              <Anticlockwise />
            </View>
            <Text className="text-gray-800 text-base font-light">{item.searchText}</Text>
          </Pressable>
        )}
        contentContainerStyle={{
          flexGrow: 1,
          backgroundColor: 'white',
          paddingBottom: 20,
        }}
      />
    </View>
  );
};

export default RecentSearches;
