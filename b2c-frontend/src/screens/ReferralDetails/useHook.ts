import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { setReferralCode } from '@/src/redux/slices/user/userSlice';
import type { AppDispatch } from '@/src/redux/store';
import { createReferralCodeAPI, fetchReferredPeopleAPI } from '@/src/networks/referral';
import type { ReferredPerson } from '@/src/networks/referral/types';

export const useReferralCode = () => {
  const dispatch = useDispatch<AppDispatch>();
  const currentUser = useSelector(selectCurrentUser);
  const [loading, setLoading] = useState(true);
  const [referred, setReferred] = useState<ReferredPerson[]>([]);
  const [totalPointsEarned, setTotalPointsEarned] = useState<number>(0);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const PAGE_SIZE = 10;
  const referralCode = currentUser?.referralCode || '';

  useEffect(() => {
    const fetchReferralCode = async () => {
      if (!currentUser?.profileId) {
        setLoading(false);
        return;
      }
      try {
        const response = await createReferralCodeAPI({ profileId: currentUser.profileId });
        if (response?.code) {
          dispatch(setReferralCode(response.code));
        }
      } catch (err) {
        console.error('Failed to fetch referral code:', err);
      }
    };
    fetchReferralCode();
  }, [currentUser?.profileId, dispatch]);

  const fetchReferred = async (reset = false) => {
    try {
      const nextPage = reset ? 1 : page;
      const response = await fetchReferredPeopleAPI({ page: nextPage, pageSize: PAGE_SIZE });
      setReferred((prev) => (reset ? response.data : [...prev, ...response.data]));
      setTotal(response.total);
      setTotalPointsEarned(response.totalPointsEarned);
      setPage((prev) => (reset ? 2 : prev + 1));
    } catch (err) {
      console.error('Failed to fetch referred users:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (currentUser?.profileId && referralCode) {
      fetchReferred(true);
    }
  }, [currentUser?.profileId, referralCode]);

  const loadMore = () => {
    if (referred.length < total && !loading) {
      fetchReferred(false);
    }
  };

  return {
    loading,
    referralCode,
    referred,
    loadMore,
    hasMore: referred.length < total,
    totalPointsEarned,
  };
};
