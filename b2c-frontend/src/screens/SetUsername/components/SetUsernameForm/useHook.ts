import { useEffect, useState, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { saveUsernameAsync } from '@/src/redux/slices/user/userSlice';
import type { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { navigateBasedOnUserState } from '@/src/utilities/navigation';
import { showToast } from '@/src/utilities/toast';
import APIResError from '@/src/errors/networks/APIResError';
import { checkUsernameAPI } from '@/src/networks/profile/username';
import type { SetUsernameFormDataI } from './types';

const useSetUsername = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationStatus, setValidationStatus] = useState<
    'idle' | 'checking' | 'valid' | 'invalid'
  >('idle');

  const dispatch = useDispatch<AppDispatch>();
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastCheckedUsernameRef = useRef<string>('');
  const currentUser = useSelector(selectCurrentUser);

  const methods = useForm<SetUsernameFormDataI>({
    mode: 'onChange',
    defaultValues: {
      userName: '',
    },
  });

  const { watch, setError, clearErrors } = methods;
  const userName = watch('userName').trim();

  const checkUsername = async (username: string) => {
    if (!username || username.length < 4) {
      setValidationStatus('idle');
      lastCheckedUsernameRef.current = '';
      clearErrors('userName');
      return;
    }

    if (username === lastCheckedUsernameRef.current) {
      return;
    }

    setValidationStatus('checking');
    lastCheckedUsernameRef.current = username;

    try {
      await checkUsernameAPI({ username });
      setValidationStatus('valid');
      clearErrors('userName');
    } catch (error) {
      setValidationStatus('invalid');

      if (error instanceof APIResError && error.status === 409) {
        setError('userName', {
          type: 'manual',
          message: 'Username is already taken',
        });
      } else {
        setError('userName', {
          type: 'manual',
          message: 'Error checking username',
        });
        showToast({
          message: 'Server Error',
          description: 'Please try again later',
          type: 'error',
        });
      }
    }
  };

  useEffect(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    const formError = methods.formState.errors.userName?.message;
    if (formError) {
      setValidationStatus('invalid');
      return;
    }

    if (userName.length === 0 || userName.length < 4) {
      setValidationStatus('idle');
      lastCheckedUsernameRef.current = '';
      clearErrors('userName');
      return;
    }

    debounceTimeoutRef.current = setTimeout(() => {
      checkUsername(userName);
    }, 800);

    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [userName, methods.formState.errors.userName]);

  const onSubmit = async (data: SetUsernameFormDataI) => {
    const trimmedUsername = data.userName.trim();

    if (trimmedUsername.length < 4) {
      setError('userName', {
        type: 'manual',
        message: 'Username must be at least 4 characters',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      await dispatch(
        saveUsernameAsync({
          username: trimmedUsername,
        }),
      ).unwrap();
      await navigateBasedOnUserState({
        isUsernameSaved: true,
        isPersonalDetailsSaved: currentUser?.isPersonalDetailsSaved ?? false,
        isWorkDetailsSaved: currentUser?.isWorkDetailsSaved ?? false,
        email: currentUser?.email ?? '',
        isEmailVerified: currentUser?.isEmailVerified ?? false,
        profileId: currentUser?.profileId ?? '',
        isPrivacyPolicyAccepted: currentUser?.isPrivacyPolicyAccepted ?? false,
        isReferred: currentUser.isReferred ?? false,
      });
    } catch (error) {
      setIsSubmitting(false);
      handleError(error);
    }
  };

  const canSubmit = methods.formState.isValid && validationStatus === 'valid';
  const isCheckingUsername = validationStatus === 'checking';

  return {
    methods,
    isSubmitting,
    onSubmit,
    canSubmit,
    isCheckingUsername,
  };
};

export default useSetUsername;
