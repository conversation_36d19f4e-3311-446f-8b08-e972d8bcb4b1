import React from 'react';
import { Pressable, Text, View } from 'react-native';
import type { DrawerNavigationProp } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import LottieLeaderboard from '@/src/components/LottieLeaderboard';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import type { RootDrawerParamListI } from '@/src/navigation/types';
import Search from '@/src/assets/svgs/Search';
import TopBarChat from '@/src/assets/svgs/TopBarChat';
import { useSocket } from '@/src/context/providers/SocketProvider';
import type { TopBarProps } from './types';

const TopBar: React.FC<TopBarProps> = React.memo(
  ({ onSearchPress, onMessagePress, onLeaderBoardPress }) => {
    const navigation = useNavigation<DrawerNavigationProp<RootDrawerParamListI>>();
    const currentUser = useSelector(selectCurrentUser);
    const { totalUnreadCount } = useSocket();

    const handleDrawerPress = () => {
      navigation.openDrawer();
    };

    return (
      <View className="px-4 py-2 flex-row items-center gap-3 bg-white">
        <Pressable className="rounded-full overflow-hidden" onPress={handleDrawerPress}>
          <UserAvatar
            avatarUri={currentUser?.avatar}
            name={currentUser?.fullName}
            width={36}
            height={36}
          />
        </Pressable>
        <Pressable
          className="flex-1 flex-row items-center px-4 py-3 bg-gray-100 rounded-xl"
          onPress={onSearchPress}
        >
          <Search />
          <Text className="ml-2 text-base text-gray-500">Search</Text>
        </Pressable>
        <Pressable className="items-center justify-center p-1" onPress={onLeaderBoardPress}>
          <LottieLeaderboard width={4} height={4} />
        </Pressable>
        <Pressable className="items-center justify-center p-1 relative" onPress={onMessagePress}>
          <TopBarChat />
          {totalUnreadCount > 0 && (
            <View className="absolute top-0 right-0 bg-green-600 rounded-full px-1.5 py-0.5 min-w-[20px] h-[20px] items-center justify-center">
              <Text className="text-white text-xs font-bold">{totalUnreadCount}+</Text>
            </View>
          )}
        </Pressable>
      </View>
    );
  },
);

export default TopBar;
