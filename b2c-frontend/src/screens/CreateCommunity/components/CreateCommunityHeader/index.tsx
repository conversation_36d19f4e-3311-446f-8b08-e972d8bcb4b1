import { View } from 'react-native';
import BackButton from '@/src/components/BackButton';
import Button from '@/src/components/Button';

interface CreateCommunityHeaderProps {
  currentPage: number;
  buttonTitle?: string;
  onNext: () => void;
  onShowDiscardModal?: (show: boolean) => void;
  onDiscard?: () => void;
  hidePageNumber?: boolean;
  totalPage?: number;
  type?: 'community' | 'forum';
  hasChanges?: boolean;
}

const CreateCommunityHeader = ({
  currentPage,
  buttonTitle,
  onNext,
  onShowDiscardModal,
  onDiscard,
  hidePageNumber,
  totalPage = 3,
  type = 'forum',
  hasChanges,
}: CreateCommunityHeaderProps) => {
  const handleClose = () => {
    if (hasChanges && onShowDiscardModal) {
      onShowDiscardModal(true);
    } else {
      onDiscard?.();
    }
  };

  return (
    <View className="flex-row justify-between items-center">
      <View className="flex-row items-center gap-3">
        <BackButton
          onBack={handleClose}
          label={!hidePageNumber ? `${currentPage}/${totalPage}` : ''}
        />
      </View>
      <View>
        <Button
          className="rounded-full bg-primaryGreen w-auto px-6 disabled:bg-gray-200 disabled:text-gray-500"
          onPress={onNext}
          label={buttonTitle ?? 'Next'}
          disabled={!hasChanges}
        />
      </View>
    </View>
  );
};

export default CreateCommunityHeader;
