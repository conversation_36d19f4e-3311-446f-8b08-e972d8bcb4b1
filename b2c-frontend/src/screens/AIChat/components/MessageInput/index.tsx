import type React from 'react';
import { useRef, useEffect, useState } from 'react';
import { View, TextInput, Pressable, Platform, ActivityIndicator, Keyboard } from 'react-native';
import Send from '@/src/assets/svgs/Send';
import type { MessageInputProps } from './types';

export const MessageInput: React.FC<MessageInputProps> = ({
  value,
  onChangeText,
  onSend,
  loading,
}) => {
  const textInputRef = useRef<TextInput>(null);
  const [keyboardHeight, setKeyboardHeight] = useState(0);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', (e) => {
      setKeyboardHeight(e.endCoordinates.height);
    });

    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardHeight(0);
    });

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);

  const handleSendPress = () => {
    if (!loading && value.trim()) {
      onSend();
    }
  };

  const handleTextChange = (text: string) => {
    onChangeText(text);
  };

  const shouldApplyKeyboardAdjustment = Platform.OS === 'android' && Platform.Version >= 35;
  const adjustedKeyboardHeight = shouldApplyKeyboardAdjustment
    ? keyboardHeight
    : Platform.OS === 'ios'
      ? 20
      : 0;

  return (
    <View
      className="flex-row items-end px-4 gap-3 border-t border-gray-200 bg-white py-6"
      style={{
        bottom: adjustedKeyboardHeight,
        paddingVertical: 8,
      }}
    >
      <View
        className="flex-1 rounded-full px-4 border"
        style={{
          backgroundColor: '#F3ECEC',
          borderColor: '#DEDEDE',
        }}
      >
        <TextInput
          ref={textInputRef}
          value={value}
          onChangeText={handleTextChange}
          placeholder="Type a message..."
          placeholderTextColor="#9CA3AF"
          className="text-sm text-gray-900 py-3"
          numberOfLines={1}
          textAlignVertical="center"
          editable={!loading}
          returnKeyType={Platform.OS === 'android' ? 'none' : 'default'}
          blurOnSubmit={Platform.OS === 'android'}
        />
      </View>

      <Pressable
        className={`w-11 h-11 rounded-full items-center justify-center ${
          value.trim() ? 'bg-green-800' : 'bg-gray-400'
        } ${loading ? 'opacity-60' : ''}`}
        onPress={handleSendPress}
        disabled={loading || !value.trim()}
      >
        {loading ? (
          <ActivityIndicator size="small" color="white" />
        ) : (
          <Send color="white" width={2} height={2} />
        )}
      </Pressable>
    </View>
  );
};
