/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { useState, useEffect } from 'react';
import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { useSelector } from 'react-redux';
import SafeArea from '@/src/components/SafeArea';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { navigateBasedOnUserState } from '@/src/utilities/navigation';
import PersonalDetails from './components/AddPersonalDetailForm';
import { PersonalDetailsFormDataI } from './components/AddPersonalDetailForm/types';
import WorkDetails from './components/AddWorkDetailsForm';
import { WorkDetailsFormDataI } from './components/AddWorkDetailsForm/types';

const AddUserDetails = () => {
  const currentUser = useSelector(selectCurrentUser);

  const [currentStepIndex, setCurrentStepIndex] = useState(
    currentUser?.isPersonalDetailsSaved ? 1 : 0,
  );
  const [personalData, setPersonalData] = useState<PersonalDetailsFormDataI>();
  const [workData, setWorkData] = useState<WorkDetailsFormDataI>();

  useEffect(() => {
    if (!currentUser) return;

    setPersonalData({
      fullName: currentUser.fullName || '',
      country: currentUser.country,
      gender: currentUser.gender || '',
    });

    if (currentUser.designation?.id || currentUser.organisation?.id) {
      setWorkData({
        designation: currentUser.designation!,
        entity: currentUser.organisation!,
      });
    }
  }, [currentUser]);

  const handlePersonalDetailsNext = (data: PersonalDetailsFormDataI) => {
    setPersonalData(data);
    setCurrentStepIndex(1);
  };

  const handleWorkDetailsNext = async (data: WorkDetailsFormDataI) => {
    setWorkData(data);
    await navigateBasedOnUserState({
      isUsernameSaved: currentUser?.isUsernameSaved ?? false,
      isPersonalDetailsSaved: currentUser?.isPersonalDetailsSaved ?? false,
      isWorkDetailsSaved: true,
      email: currentUser?.email ?? '',
      isEmailVerified: currentUser?.isEmailVerified ?? false,
      profileId: currentUser?.profileId ?? '',
      isPrivacyPolicyAccepted: currentUser?.isPrivacyPolicyAccepted ?? false,
      isReferred: currentUser.isReferred ?? false,
    });
  };

  const handleBack = () => {
    setCurrentStepIndex(0);
  };

  const defaultPersonalData = {
    fullName: personalData?.fullName!,
    country: personalData?.country!,
    gender: personalData?.gender!,
  };

  return (
    <SafeArea>
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          style={{ flex: 1 }}
        >
          <View className="flex-1 p-5">
            {currentStepIndex === 0 ? (
              <PersonalDetails onNext={handlePersonalDetailsNext} />
            ) : (
              <WorkDetails
                onNext={handleWorkDetailsNext}
                onBack={handleBack}
                personalData={defaultPersonalData}
                initialData={workData}
              />
            )}
          </View>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </SafeArea>
  );
};

export default AddUserDetails;
