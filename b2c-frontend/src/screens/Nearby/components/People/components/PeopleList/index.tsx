import { ActivityIndicator, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import UsersList from '@/src/components/UsersList';
import { ListItem } from '@/src/components/UsersList/types';
import { NearbyStackParamListI } from '@/src/navigation/types';
import type { PeopleListProps } from './types';

const renderDistance = (distance: number) => (
  <View className="bg-[#DDEFC8] rounded-lg min-w-[80px] items-center justify-center px-2 py-1">
    <Text className="text-[#448600] text-xs font-medium">{distance} km away</Text>
  </View>
);

const PeopleList = ({ data, onLoadMore, loading, isAnonymous }: PeopleListProps) => {
  const navigation = useNavigation<StackNavigationProp<NearbyStackParamListI>>();
  const hasData = data && data.length > 0;

  const onUserPress = (item: ListItem) => {
    navigation.navigate('OtherUserProfile', {
      profileId: item.Profile.id,
      fromTabPress: false,
    });
  };

  if (isAnonymous) {
    return (
      <View className="flex-1 items-center justify-center px-6 mt-10">
        <View className="items-center max-w-[300px]">
          <Text className="text-lg font-semibold text-gray-800 text-center mb-2">
            Discover Nearby Connections
          </Text>
          <Text className="text-gray-600 text-center mb-6">
            Turn off anonymous mode to see and be seen by others nearby.
          </Text>
        </View>
      </View>
    );
  }

  if (loading && !hasData) {
    return (
      <View className="flex-1 justify-center items-center py-5">
        <ActivityIndicator size="small" color="#6B7280" />
        <Text className="mt-2 text-base text-gray-600 text-center">Finding mariners nearby...</Text>
      </View>
    );
  }

  if (!loading && !hasData) {
    return (
      <View className="flex-1">
        <View className="px-4 pt-2">
          <Text className="text-base font-medium text-gray-800">Nearby People</Text>
        </View>
        <View className="flex-1 items-center justify-center px-6">
          <Text className="text-lg font-medium text-gray-700 text-center mb-2">
            No one nearby right now.
          </Text>
          <Text className="text-base text-gray-500 text-center">
            Hang tight, someone might show up soon!
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View className="flex-1">
      <View className="px-4 pt-2 pb-1">
        <Text className="text-base font-medium text-gray-800">Nearby People</Text>
      </View>
      <UsersList
        data={data}
        onLoadMore={onLoadMore}
        loading={loading}
        onPress={onUserPress}
        renderActions={(item) => renderDistance(item.distanceInMetres as number)}
      />
    </View>
  );
};

export default PeopleList;
