import { useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Pressable, ScrollView, Text, View } from 'react-native';
import { useNavigation, type NavigationAction } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import Chip from '@/src/components/Chip';
import EntitySearch from '@/src/components/EntitySearch';
import CustomModal from '@/src/components/Modal';
import NotFound from '@/src/components/NotFound';
import Tabs from '@/src/components/Tabs';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import type { EditSkillsListPropsI } from './types';
import { useEditSkillsList } from './useHook';

const EditSkillsList = ({ onBack, profileId, category, editable }: EditSkillsListPropsI) => {
  const {
    maritimeSkills,
    otherSkills,
    isSubmitting,
    handleSubmit,
    setMaritimeSkills,
    setOtherSkills,
    loading,
    loadingMore,
    loadMoreMaritime,
    loadMoreOther,
    hasMoreMaritime,
    hasMoreOther,
    activeTab,
    setActiveTab,
    hasChanges,
  } = useEditSkillsList(profileId, category);
  const navigation = useNavigation();
  const [showDiscardModal, setShowDiscardModal] = useState(false);
  const currentUser = useSelector(selectCurrentUser);
  const exitActionRef = useRef<NavigationAction | null>(null);
  const isOwnProfile = currentUser.profileId === profileId;

  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', (e) => {
      if (hasChanges && !isSubmitting) {
        e.preventDefault();
        exitActionRef.current = e.data.action;
        setShowDiscardModal(true);
      }
    });
    return unsubscribe;
  }, [navigation, hasChanges, isSubmitting]);
  const handleDiscardConfirm = () => {
    setShowDiscardModal(false);
    setTimeout(() => {
      if (exitActionRef.current) {
        navigation.dispatch(exitActionRef.current);
      }
    }, 300);
  };
  const handleDiscardCancel = () => {
    setShowDiscardModal(false);
  };
  const tabs = [
    { id: 'maritimeSkills', label: 'Maritime Skills' },
    { id: 'otherSkills', label: 'Other Skills' },
  ];
  if (loading) {
    return (
      <View className="flex-1 justify-center items-center">
        <ActivityIndicator size="large" color="#448600" />
      </View>
    );
  }
  const currentSkills = activeTab === 'maritimeSkills' ? maritimeSkills : otherSkills;
  const hasMore = activeTab === 'maritimeSkills' ? hasMoreMaritime : hasMoreOther;
  const loadMore = activeTab === 'maritimeSkills' ? loadMoreMaritime : loadMoreOther;
  const handleScrollEndDrag = () => {
    if (hasMore && !loadingMore) {
      loadMore();
    }
  };
  const handleRemoveSkill = (itemId: string) => {
    if (activeTab === 'maritimeSkills') {
      setMaritimeSkills((prev) => prev.filter((s) => s.id !== itemId));
    } else {
      setOtherSkills((prev) => prev.filter((s) => s.id !== itemId));
    }
  };
  return (
    <View className="flex-1 pb-10">
      <View className="flex-row items-center justify-between px-4 py-3">
        <BackButton onBack={onBack} label="" />
        {editable && profileId === currentUser.profileId && (
          <Pressable onPress={handleSubmit} disabled={isSubmitting || !hasChanges}>
            <Text
              className={`text-lg font-medium ${isSubmitting || !hasChanges ? 'text-gray-400' : 'text-[#448600]'}`}
            >
              {isSubmitting ? 'Saving...' : 'Save'}
            </Text>
          </Pressable>
        )}
      </View>
      <View className="px-4">
        <Tabs tabs={tabs} activeTab={activeTab} onTabChange={setActiveTab} />
      </View>
      <View className="flex-1 px-4">
        <View className="pb-4">
          {editable && isOwnProfile && (
            <EntitySearch
              title=""
              placeholder="Add a skill"
              selectionKey="skill"
              multipleSelection={true}
              className="mt-1"
            />
          )}
        </View>
        <ScrollView
          style={{ flex: 1 }}
          showsVerticalScrollIndicator={false}
          onScrollEndDrag={handleScrollEndDrag}
          contentContainerStyle={
            currentSkills.length === 0 ? { flexGrow: 1, justifyContent: 'center' } : {}
          }
        >
          {currentSkills.length === 0 ? (
            <NotFound
              title={
                profileId === currentUser.profileId ? 'No skills added' : 'No skills available'
              }
              subtitle={
                profileId === currentUser.profileId
                  ? 'Start by adding skills to this category.'
                  : 'This user has not added any skills to this category.'
              }
              imageStyle={{ width: 150, height: 150 }}
              fullScreen={false}
              className="mb-10"
            />
          ) : (
            <View
              style={{
                flexDirection: 'row',
                flexWrap: 'wrap',
                paddingBottom: 20,
              }}
            >
              {currentSkills.map((item) => (
                <View key={item.id} style={{ marginRight: 8, marginBottom: 8 }}>
                  <Chip
                    label={item.name}
                    removable={editable && profileId === currentUser.profileId}
                    onRemove={() => handleRemoveSkill(item.id)}
                    labelClassName="text-sm"
                  />
                </View>
              ))}
            </View>
          )}
          {loadingMore && (
            <View style={{ padding: 20, alignItems: 'center' }}>
              <ActivityIndicator size="small" color="#448600" />
            </View>
          )}
        </ScrollView>
      </View>
      <CustomModal
        isVisible={showDiscardModal}
        title="Discard changes?"
        description="You have unsaved changes. Are you sure you want to discard them and leave this screen?"
        onCancel={handleDiscardCancel}
        onConfirm={handleDiscardConfirm}
        cancelText="Cancel"
        confirmText="Discard"
        confirmButtonVariant="danger"
      />
    </View>
  );
};
export default EditSkillsList;
