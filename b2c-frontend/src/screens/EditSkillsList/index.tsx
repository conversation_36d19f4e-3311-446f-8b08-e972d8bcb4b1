import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useSelector } from 'react-redux';
import SafeArea from '@/src/components/SafeArea';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { ProfileStackParamsListI } from '@/src/navigation/types';
import EditSkillsList from './components/EditSkillsList';

type RouteProps = RouteProp<ProfileStackParamsListI, 'EditSkillsList'>;

const EditSkillsListScreen = () => {
  const route = useRoute<RouteProps>();
  const { category, profileId, editable } = route.params || {};
  const navigation = useNavigation<StackNavigationProp<ProfileStackParamsListI>>();

  return (
    <SafeArea>
      <EditSkillsList
        profileId={profileId ? profileId : useSelector(selectCurrentUser).profileId}
        category={category!}
        onBack={navigation.goBack}
        editable={editable}
      />
    </SafeArea>
  );
};

export default EditSkillsListScreen;
