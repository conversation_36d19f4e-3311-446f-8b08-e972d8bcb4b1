import { View } from 'react-native';
import { Controller } from 'react-hook-form';
import { useSelector } from 'react-redux';
import Button from '@/src/components/Button';
import TextInput from '@/src/components/TextInput';
import TextView from '@/src/components/TextView';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { navigateBasedOnUserState } from '@/src/utilities/navigation';
import useStorage from '@/src/hooks/storage';
import useReferralCodeForm from './useHook';

const ReferralCode = () => {
  const { methods, isSubmitting, onSubmit, canSubmit } = useReferralCodeForm();
  const { control, handleSubmit } = methods;
  const { setStorage } = useStorage();
  const currentUser = useSelector(selectCurrentUser);

  const handleSkip = async () => {
    await setStorage('skippedReferral', 'true');
    await navigateBasedOnUserState({
      isUsernameSaved: currentUser?.isUsernameSaved ?? false,
      isPersonalDetailsSaved: currentUser?.isPersonalDetailsSaved ?? false,
      isWorkDetailsSaved: currentUser?.isWorkDetailsSaved ?? false,
      email: currentUser?.email ?? '',
      isEmailVerified: currentUser?.isEmailVerified ?? false,
      profileId: currentUser?.profileId ?? '',
      isPrivacyPolicyAccepted: currentUser?.isPrivacyPolicyAccepted ?? false,
      isReferred: currentUser.isReferred ?? false,
    });
  };

  return (
    <View className="flex-1 bg-white justify-between">
      <View className="px-5">
        <View className="my-8">
          <TextView title="Enter Referral Code" subtitle="If you have one, enter it here." />
        </View>

        <Controller
          control={control}
          name="referralCode"
          render={({ field: { onChange, value, onBlur }, fieldState: { error } }) => (
            <TextInput
              label="Referral Code"
              placeholder="e.g. RHSGWBCQ"
              value={value}
              onChangeText={onChange}
              onBlur={onBlur}
              error={error?.message}
              autoCapitalize="characters"
            />
          )}
        />

        <View className="mt-8">
          <Button
            onPress={handleSubmit(onSubmit)}
            disabled={!canSubmit || isSubmitting}
            label="Submit"
            variant={canSubmit ? 'primary' : 'tertiary'}
            loading={isSubmitting}
            labelClassName="text-base font-medium"
          />
        </View>

        <View className="mt-4">
          <Button
            onPress={handleSkip}
            label="Skip this step"
            variant="secondary"
            labelClassName="text-base text-gray-500"
          />
        </View>
      </View>
    </View>
  );
};

export default ReferralCode;
