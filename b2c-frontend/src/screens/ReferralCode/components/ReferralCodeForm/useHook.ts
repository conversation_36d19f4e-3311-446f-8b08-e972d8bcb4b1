import { useState } from 'react';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { setIsReferred, setReferredByCode } from '@/src/redux/slices/user/userSlice';
import { AppDispatch } from '@/src/redux/store';
import { handleError } from '@/src/utilities/errors/errors';
import { showToast } from '@/src/utilities/toast';
import type { AppStackParamListI } from '@/src/navigation/types';
import { createReferralStatusAPI } from '@/src/networks/referral';
import type { ReferralCodeFormDataI } from './types';

const useReferralCodeForm = () => {
  const navigation = useNavigation<StackNavigationProp<AppStackParamListI>>();
  const dispatch = useDispatch<AppDispatch>();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const currentUser = useSelector(selectCurrentUser);

  const methods = useForm<ReferralCodeFormDataI>({
    mode: 'onChange',
    defaultValues: {
      referralCode: '',
    },
  });

  const onSubmit = async (data: ReferralCodeFormDataI) => {
    setIsSubmitting(true);
    try {
      dispatch(setReferredByCode(data.referralCode));
      await createReferralStatusAPI({
        referralCode: data.referralCode,
        referredProfileId: currentUser.profileId,
      });
      dispatch(setIsReferred(true));
      showToast({ message: 'Referral code accepted', type: 'success' });
      navigation.navigate('SetUsername');
    } catch (error) {
      handleError(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    methods,
    isSubmitting,
    onSubmit,
    canSubmit: methods.formState.isValid,
  };
};

export default useReferralCodeForm;
