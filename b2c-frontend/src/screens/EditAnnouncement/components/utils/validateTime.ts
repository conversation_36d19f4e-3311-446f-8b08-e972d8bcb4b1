export const validateTime = (startTime: string | Date, endTime: string | Date): true | string => {
  const start = typeof startTime === 'string' ? new Date(`1970-01-01T${startTime}`) : startTime;
  const end = typeof endTime === 'string' ? new Date(`1970-01-01T${endTime}`) : endTime;

  if (!(start instanceof Date) || isNaN(start.getTime())) return 'Invalid start time';
  if (!(end instanceof Date) || isNaN(end.getTime())) return 'Invalid end time';

  if (end <= start) return 'End time must be after start time';

  return true;
};
