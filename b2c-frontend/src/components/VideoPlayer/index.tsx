import React, { useState, useRef } from 'react';
import { View, Text, Pressable, Dimensions, ActivityIndicator } from 'react-native';
import Video from 'react-native-video';
import PauseIcon from '@/src/assets/svgs/Pause';
import PlayIcon from '@/src/assets/svgs/Play';
import type { VideoPlayerProps } from './types';

const SCREEN_WIDTH = Dimensions.get('window').width;
const CONTENT_WIDTH = SCREEN_WIDTH - 32;

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  source,
  width = CONTENT_WIDTH,
  height = CONTENT_WIDTH,
  showControls = true,
  autoPlay = false,
  loop = false,
  muted = false,
  resizeMode = 'cover',
  onPress,
  style,
  borderRadius = 12,
  controlButtonSize = 16,
}) => {
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [showControlsOverlay, setShowControlsOverlay] = useState(true);
  const [showCenterControls, setShowCenterControls] = useState(true);
  const [hasEnded, setHasEnded] = useState(false);
  const [showReloadButton, setShowReloadButton] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const videoRef = useRef<any>(null);

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handlePlayPause = () => {
    if (hasEnded) {
      handleReload();
      return;
    }

    setIsPlaying(!isPlaying);
    setShowControlsOverlay(true);
    setShowCenterControls(true);

    if (!isPlaying) {
      setTimeout(() => {
        setShowCenterControls(false);
        setShowControlsOverlay(false);
      }, 4000);
    }
  };

  const handleReload = () => {
    setCurrentTime(0);
    setHasEnded(false);
    setShowReloadButton(false);
    setIsPlaying(true);
    setShowControlsOverlay(true);
    setShowCenterControls(true);
    videoRef.current?.seek(0);
    setTimeout(() => {
      setShowCenterControls(false);
      setShowControlsOverlay(false);
    }, 3000);
  };

  const handleVideoPress = () => {
    if (onPress) {
      onPress();
    } else {
      setShowControlsOverlay(true);
      setShowCenterControls(true);
      if (isPlaying && !hasEnded) {
        setTimeout(() => {
          setShowCenterControls(false);
          setShowControlsOverlay(false);
        }, 4000);
      }
    }
  };

  const handleLoad = (data: any) => {
    setDuration(data.duration);
    setIsLoading(false);
  };

  const handleProgress = (data: any) => {
    setCurrentTime(data.currentTime);
    if (hasEnded && data.currentTime > 0 && data.currentTime < duration - 1) {
      setHasEnded(false);
      setShowReloadButton(false);
    }
  };

  const handleEnd = () => {
    setIsPlaying(false);
    setHasEnded(true);
    setShowReloadButton(true);
    setShowControlsOverlay(true);
    setShowCenterControls(true);
  };

  return (
    <View style={[{ width: width as number, height: height as number }, style]}>
      <Pressable
        onPress={handleVideoPress}
        style={{
          width: '100%',
          height: '100%',
          borderRadius,
          overflow: 'hidden',
        }}
      >
        {isLoading && (
          <View
            style={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              backgroundColor: '#1c1c1e',
              alignItems: 'center',
              justifyContent: 'center',
              zIndex: 2,
            }}
          >
            <ActivityIndicator size="large" color="#ffffff" />
          </View>
        )}

        <Video
          ref={videoRef}
          source={{ uri: source }}
          style={{ width: '100%', height: '100%' }}
          paused={!isPlaying}
          repeat={loop}
          muted={muted}
          resizeMode={resizeMode}
          onLoad={handleLoad}
          onProgress={handleProgress}
          onEnd={handleEnd}
          progressUpdateInterval={1000}
        />

        {showControls && showControlsOverlay && (
          <View
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: 'rgba(0, 0, 0, 0.2)',
            }}
          >
            {showCenterControls && (
              <Pressable
                onPress={showReloadButton ? handleReload : handlePlayPause}
                style={{
                  backgroundColor: 'rgba(0, 0, 0, 0.7)',
                  borderRadius: 35,
                  width: 70,
                  height: 70,
                  justifyContent: 'center',
                  alignItems: 'center',
                  shadowColor: '#000',
                  shadowOffset: { width: 0, height: 4 },
                  shadowOpacity: 0.4,
                  shadowRadius: 8,
                  elevation: 10,
                  borderWidth: 2,
                  borderColor: 'rgba(255, 255, 255, 0.2)',
                }}
                hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
              >
                {showReloadButton ? (
                  <View style={{ alignItems: 'center', justifyContent: 'center' }}>
                    <PlayIcon width={4} height={4} fill="#FFFFFF" />
                    <Text
                      style={{
                        fontSize: 8,
                        color: '#FFFFFF',
                        fontWeight: '600',
                        textAlign: 'center',
                        letterSpacing: 0.5,
                      }}
                    >
                      REPLAY
                    </Text>
                  </View>
                ) : isPlaying ? (
                  <PauseIcon width={6} height={6} fill="#FFFFFF" />
                ) : (
                  <PlayIcon width={6} height={6} fill="#FFFFFF" style={{ marginLeft: 3 }} />
                )}
              </Pressable>
            )}

            <View
              style={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                paddingHorizontal: 12,
                paddingVertical: 8,
              }}
            >
              {duration > 0 && (
                <View
                  style={{
                    height: 2,
                    backgroundColor: 'rgba(255, 255, 255, 0.3)',
                    borderRadius: 1,
                    marginBottom: 6,
                  }}
                >
                  <View
                    style={{
                      height: '100%',
                      backgroundColor: '#fff',
                      borderRadius: 1,
                      width: `${(currentTime / duration) * 100}%`,
                    }}
                  />
                </View>
              )}
              {duration > 0 && (
                <Text style={{ color: 'white', fontSize: 10, textAlign: 'right', opacity: 0.9 }}>
                  {formatTime(currentTime)} / {formatTime(duration)}
                </Text>
              )}
            </View>
          </View>
        )}
      </Pressable>
    </View>
  );
};

export default VideoPlayer;
