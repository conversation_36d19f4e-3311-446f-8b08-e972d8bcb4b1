export interface CustomModalProps {
  isVisible: boolean;
  title: string;
  description?: string;
  cancelText?: string;
  confirmText?: string;
  onConfirm: (inputValue?: string) => void;
  onCancel?: () => void;
  inputPlaceholder?: string;
  inputValue?: string;
  onInputChange?: (text: string) => void;
  inputType?: 'default' | 'number' | 'email' | 'password';
  inputRequired?: boolean;
  inputLabel?: string;
  bodyComponent?: React.ReactNode;
  isConfirming?: boolean;
  confirmButtonVariant?: 'default' | 'danger';
  cancelBtnClassName?: string;
  confirmBtnClassName?: string;
  cancelButtonMode?: 'default' | 'danger' | 'outlined';
  confirmButtonMode?: 'default' | 'danger' | 'outlined';
  disableConfirm?: boolean;
}
