import {
  View,
  Text,
  ScrollView,
  Modal,
  Pressable,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import Markdown from 'react-native-markdown-display';
import type { PrivacyModalProps } from './types';
import { usePrivacyModal } from './useHook';

const PrivacyModal = ({ isVisible, onClose }: PrivacyModalProps) => {
  const { policyContent, loading } = usePrivacyModal(isVisible);

  const cleanContent = policyContent
    ?.replace(/\\\n/g, '\n')
    .replace(/\\"/g, '"')
    .replace(/\\'/g, "'");

  const markdownStyles = StyleSheet.create({
    body: {
      fontSize: 16,
      lineHeight: 24,
      color: '#374151',
    },
    heading1: {
      fontSize: 24,
      fontWeight: 'bold',
      color: '#111827',
      marginBottom: 16,
      marginTop: 24,
    },
    heading2: {
      fontSize: 20,
      fontWeight: 'bold',
      color: '#111827',
      marginBottom: 12,
      marginTop: 20,
    },
    heading3: {
      fontSize: 18,
      fontWeight: '600',
      color: '#111827',
      marginBottom: 8,
      marginTop: 16,
    },
    paragraph: {
      marginBottom: 12,
      lineHeight: 22,
    },
    list_item: {
      marginBottom: 8,
    },
    bullet_list: {
      marginBottom: 16,
    },
    ordered_list: {
      marginBottom: 16,
    },
    strong: {
      fontWeight: 'bold',
    },
    em: {
      fontStyle: 'italic',
    },
    link: {
      color: '#004687',
      textDecorationLine: 'underline',
    },
  });

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View className="flex-1 bg-white">
        <View className="flex-row justify-between items-center p-4 border-b border-gray-200">
          <Text className="text-xl font-bold text-gray-900">Privacy Policy</Text>
          <Pressable onPress={onClose} className="p-2">
            <Text className="text-lg text-blue-600 font-medium">Done</Text>
          </Pressable>
        </View>
        {loading ? (
          <View className="flex-1 justify-center items-center">
            <ActivityIndicator size="small" color="#448600" />
          </View>
        ) : (
          <ScrollView
            className="flex-1 px-4"
            contentContainerStyle={{ paddingVertical: 20 }}
            showsVerticalScrollIndicator={false}
          >
            {cleanContent && <Markdown style={markdownStyles}>{cleanContent}</Markdown>}
          </ScrollView>
        )}
      </View>
    </Modal>
  );
};

export default PrivacyModal;
