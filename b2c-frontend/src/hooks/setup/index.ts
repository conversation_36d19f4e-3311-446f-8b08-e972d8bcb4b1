import { generateUUIDv4 } from '@/src/utilities/data/string';
import useNotification from '@/src/hooks/notification';
import { requestPermissions } from '@/src/hooks/permission';
import useStorage from '@/src/hooks/storage';

const useSetup = () => {
  const { getStorage, setStorage } = useStorage();
  const { setupNotification, getDeviceToken, subscribeToPublicTopic } = useNotification();

  const setupStorage = async () => {
    let deviceId = await getStorage('deviceId');
    if (!deviceId) {
      deviceId = generateUUIDv4();
      await setStorage('deviceId', deviceId);
    }
  };

  const setup = async () => {
    try {
      await setupStorage();
      const hasPermission = await requestPermissions();

      if (hasPermission) {
        await setupNotification();
        await getDeviceToken(true);
        await subscribeToPublicTopic();
      }
    } catch (error) {
      console.error('Setup error:', error);
    }
  };

  return { setup };
};

export default useSetup;
