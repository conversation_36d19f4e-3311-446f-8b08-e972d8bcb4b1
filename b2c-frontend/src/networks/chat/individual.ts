import { apiCall } from '@/src/services/api';
import {
  ChatsResponse,
  IndividualChatDeleteManySpecificProfile,
  IndividualChatFindAll,
  IndividualChatFindAllSpecificProfile,
  MessageListResponse,
} from './types';

export const findAllIndividualChats = async (
  params: IndividualChatFindAll,
): Promise<ChatsResponse> => {
  const result = await apiCall<unknown, ChatsResponse>('/chat/api/v1/individual-chats', 'GET', {
    isAuth: true,
    query: params,
  });

  return result;
};

export const findAllSpecificProfileChats = async (
  params: IndividualChatFindAllSpecificProfile,
): Promise<MessageListResponse> => {
  const result = await apiCall<unknown, MessageListResponse>(
    '/chat/api/v1/individual-chat/profile',
    'GET',
    {
      isAuth: true,
      query: params,
    },
  );
  return result;
};

export const deleteManySpecificProfileChats = async (
  payload: IndividualChatDeleteManySpecificProfile,
) => {
  const result = await apiCall<unknown, unknown>('/chat/api/v1/individual-chats', 'DELETE', {
    isAuth: true,
    payload,
  });
  return result;
};

export const deleteManyAllChats = async (payload: { profileIds: string[] }) => {
  const result = await apiCall<unknown, unknown>('/chat/api/v1/individual-chats/all', 'DELETE', {
    isAuth: true,
    payload,
  });
  return result;
};

export const deleteSpecificProfileChat = async (routeId: string) => {
  const result = await apiCall<unknown, unknown>(`/chat/api/v1/individual-chat/profile`, 'DELETE', {
    isAuth: true,
    routeId,
  });
  return result;
};
