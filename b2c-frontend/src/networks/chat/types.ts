import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { ObjUnknownI } from '@/src/types/common/data';

export type MimeType = 'JPEG' | 'TEXT' | 'PDF';

export interface Media {
  id: string;
  url: string;
  mimeType: MimeType;
  name?: string;
}

export interface MessageContent {
  text?: string;
  media: Media[];
}

export interface IndividualChatSaveMessageData {
  id?: string;
  senderId?: string;
  recieverId?: string;
  content?: MessageContent;
  replyTo?: string;
  readAt?: Date;
}

export interface Pagination {
  page: number;
  pageSize?: number;
}

export interface IndividualChatFindAll extends Pagination, ObjUnknownI {}

export interface IndividualChatFindAllSpecificProfile extends Pagination, ObjUnknownI {
  profileId: string;
}

export interface IndividualChatDeleteManyAll {
  profileIds: string[];
}

export interface IndividualChatDeleteManySpecificProfile {
  ids: string[];
  profileId: string;
  deleteType: 'FOR_ME' | 'FOR_EVERYONE';
}

export interface IndividualChatDeleteForEveryone {
  id: string;
}

export interface IndividualChatEditMessage {
  id: string;
  content: MessageContent;
}

export interface ProfileData {
  email: string;
  name: string;
  username: string;
  avatar: string | null;
  profileId: string;
  designation: SearchResultI | null;
  entity: SearchResultI | null;
}

export interface ChatListItem {
  id: { $oid: string };
  senderId: string;
  recieverId: string;
  content: MessageContent;
  userStatus: {
    lastSeen: Date | null;
    status: 'online' | 'offline';
  };
  lastSeen: string;
  readAt: Date | null;
  createdAt: Date;
  unreadCount: number;
}

export interface MessageData {
  id: string;
  senderId: string;
  recieverId: string;
  content: {
    text?: string | null;
    media?: Array<{
      url: string;
      mimeType: string;
      name?: string | null;
    }>;
  };
  messageType: 'TEXT' | 'MEDIA' | 'MIXED';
  replyTo?: string | null;
  readAt?: Date | null;
  createdAt: Date;
  editedAt?: Date | null;
  deletedForAll: boolean;
  deletedFor: string[];
}

export interface ChatsResponse {
  data: ChatListItem[];
  total: number;
  hasMore: boolean;
  totalUnread: number;
}

export interface MessageListResponse {
  data: MessageData[];
  total: number;
  lastSeen: Date | null;
  hasMore: boolean;
}
