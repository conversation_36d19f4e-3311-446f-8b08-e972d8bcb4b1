import { SearchResultI } from '@/src/redux/slices/entitysearch/types';
import { ObjUnknownI } from '@/src/types/common/data';

export interface CreateReferralCodePayloadI {
  profileId: string;
}

export interface ReferralCodeResponseI {
  code: string;
}

export interface CreateReferralStatusPayloadI {
  referredProfileId: string;
  referralCode: string;
}

export interface CompleteOnboardingPayloadI {
  referredProfileId: string;
}

export interface ReferralStatusResponseI {
  id: string;
  referredProfileId: string;
  referralCodeId: string;
  isReferrerRewarded: boolean;
  joinedAt: string;
  createdAt: string;
  updatedAt?: string;
}

export interface Pagination extends ObjUnknownI {
  page: number;
  pageSize: number;
}

export interface ReferredPerson {
  id: string;
  name: string;
  avatar: string | null;
  designation: SearchResultI;
  entity: SearchResultI;
}

export interface ReferredPeopleResponse {
  data: ReferredPerson[];
  total: number;
  totalPointsEarned: number;
}
