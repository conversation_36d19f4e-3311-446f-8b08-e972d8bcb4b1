import { createContext, useContext, useEffect, useRef, useState } from 'react';
import { AppState, type AppStateStatus, Platform } from 'react-native';
import Config from 'react-native-config';
import useStorage from '@/src/hooks/storage';
import { SocketContextProviderProps, SocketContextValue, MessageHandler } from './types';

const SocketContext = createContext<SocketContextValue | null>(null);

const getBaseUrl = (): string => {
  if (Config.ENV === 'development') {
    const localhost = Platform.OS === 'ios' ? 'localhost' : '********';
    return `ws://${localhost}:4003`;
  }
  return Config.BASE_URL === 'https://api.b2c.navicater.com'
    ? 'wss://api.b2c.navicater.com'
    : 'wss://api.b2c.dev.navicater.com';
};

interface WebSocketMessage {
  type: string;
  data: Record<string, unknown>;
}

const SocketContextProvider = ({ children }: SocketContextProviderProps) => {
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [isConnecting, setIsConnecting] = useState<boolean>(false);
  const [socket, setSocket] = useState<WebSocket | null>(null);
  const [profileId, setProfileId] = useState<string | null>(null);
  const [totalUnreadCount, setTotalUnreadCount] = useState<number>(0);
  const { getStorage } = useStorage();

  const socketRef = useRef<WebSocket | null>(null);
  const pingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const connectionTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const mountedRef = useRef<boolean>(true);
  const retryCountRef = useRef<number>(0);
  const isConnectingRef = useRef<boolean>(false);
  const forceConnect = useRef<boolean>(true);
  const lastPingTime = useRef<number>(Date.now());
  const missedPings = useRef<number>(0);
  const messageHandlers = useRef<Map<string, Set<MessageHandler>>>(new Map());

  const sendPing = () => {
    if (socketRef.current?.readyState === WebSocket.OPEN) {
      try {
        const pingMessage: WebSocketMessage = {
          type: 'ping',
          data: { timestamp: Date.now() },
        };
        socketRef.current.send(JSON.stringify(pingMessage));
        lastPingTime.current = Date.now();
      } catch (error) {
        missedPings.current++;
        if (missedPings.current >= 3) {
          forceReconnect();
        }
      }
    } else if (forceConnect.current && profileId) {
      connect();
    }
  };

  const startPingInterval = (): void => {
    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current);
    }
    pingIntervalRef.current = setInterval(sendPing, 10000);
  };

  const stopAllIntervals = (): void => {
    if (pingIntervalRef.current) {
      clearInterval(pingIntervalRef.current);
      pingIntervalRef.current = null;
    }
  };

  const clearTimeouts = (): void => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
    if (connectionTimeoutRef.current) {
      clearTimeout(connectionTimeoutRef.current);
      connectionTimeoutRef.current = null;
    }
  };

  const forceReconnect = () => {
    if (socketRef.current) {
      socketRef.current.close();
    }
    setIsConnected(false);
    isConnectingRef.current = false;
    if (forceConnect.current) {
      setTimeout(() => connect(), 1000);
    }
  };

  const broadcastMessage = (
    type: string,
    data: Record<string, unknown>,
    rawMessage: Record<string, unknown>,
  ): void => {
    if (type === 'pong') {
      missedPings.current = 0;
      lastPingTime.current = Date.now();
      return;
    }

    const handlers = messageHandlers.current.get(type);
    if (handlers && handlers.size > 0) {
      handlers.forEach((handler) => {
        try {
          handler(type, data, rawMessage);
        } catch (error) {}
      });
    }
  };

  const scheduleReconnect = (): void => {
    if (!forceConnect.current) {
      return;
    }

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }

    const delay = Math.min(5000, 1000 + Math.random() * 2000);
    reconnectTimeoutRef.current = setTimeout(async () => {
      if (forceConnect.current && !isConnected && !isConnectingRef.current) {
        await connect();
      }
    }, delay);
  };

  const connect = async (): Promise<void> => {
    if (!profileId || !forceConnect.current) {
      return;
    }

    if (isConnectingRef.current) {
      return;
    }

    if (isConnected && socketRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    isConnectingRef.current = true;
    setIsConnecting(true);

    try {
      if (socketRef.current) {
        socketRef.current.close();
        socketRef.current = null;
      }

      const baseUrl = getBaseUrl();
      const wsUrl = `${baseUrl}/ws/chat/${profileId}`;

      const ws = new WebSocket(wsUrl, [], {
        headers: {
          'User-Agent': 'Navicater/1.0',
        },
      });

      socketRef.current = ws;
      setSocket(ws);

      connectionTimeoutRef.current = setTimeout(() => {
        if (isConnectingRef.current && !isConnected) {
          ws.close();
          isConnectingRef.current = false;
          setIsConnecting(false);
          if (forceConnect.current) {
            scheduleReconnect();
          }
        }
      }, 8000);

      ws.onopen = () => {
        clearTimeouts();
        setIsConnected(true);
        setIsConnecting(false);
        isConnectingRef.current = false;
        retryCountRef.current = 0;
        missedPings.current = 0;
        lastPingTime.current = Date.now();
        startPingInterval();
      };

      ws.onclose = (event) => {
        clearTimeouts();
        setIsConnected(false);
        setIsConnecting(false);
        isConnectingRef.current = false;
        if (forceConnect.current) {
          scheduleReconnect();
        }
      };

      ws.onerror = (error) => {
        clearTimeouts();
        setIsConnecting(false);
        isConnectingRef.current = false;
        if (forceConnect.current) {
          scheduleReconnect();
        }
      };

      ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          broadcastMessage(message.type, message.data, { type: message.type, data: message.data });
        } catch (error) {}
      };
    } catch (error) {
      setIsConnecting(false);
      isConnectingRef.current = false;
      if (forceConnect.current) {
        scheduleReconnect();
      }
    }
  };

  const connectSocket = async (newProfileId: string): Promise<void> => {
    if (!newProfileId) {
      throw new Error('ProfileId is required');
    }

    forceConnect.current = true;

    if (socketRef.current) {
      socketRef.current.close();
    }

    setIsConnected(false);
    setIsConnecting(false);
    isConnectingRef.current = false;
    setSocket(null);
    socketRef.current = null;
    stopAllIntervals();
    clearTimeouts();

    setProfileId(newProfileId);
    retryCountRef.current = 0;
  };

  const disconnect = (permanent = false): void => {
    forceConnect.current = !permanent;

    if (socketRef.current) {
      socketRef.current.close(1000, 'Client disconnect');
    }

    setIsConnected(false);
    setIsConnecting(false);
    isConnectingRef.current = false;
    setSocket(null);
    socketRef.current = null;
    stopAllIntervals();
    clearTimeouts();

    if (permanent) {
      setProfileId(null);
    }
  };

  const sendMessage = (type: string, data: Record<string, unknown>): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (socketRef.current?.readyState === WebSocket.OPEN) {
        try {
          const message: WebSocketMessage = {
            type,
            data,
          };
          socketRef.current.send(JSON.stringify(message));
          resolve();
        } catch (error) {
          reject(error);
        }
      } else {
        if (forceConnect.current && !isConnectingRef.current) {
          connect();
        }
        reject(new Error('WebSocket not connected'));
      }
    });
  };

  const subscribeToMessages = (messageTypes: string[], handler: MessageHandler): (() => void) => {
    messageTypes.forEach((type) => {
      if (!messageHandlers.current.has(type)) {
        messageHandlers.current.set(type, new Set());
      }
      messageHandlers.current.get(type)!.add(handler);
    });

    return () => {
      messageTypes.forEach((type) => {
        const handlers = messageHandlers.current.get(type);
        if (handlers) {
          handlers.delete(handler);
          if (handlers.size === 0) {
            messageHandlers.current.delete(type);
          }
        }
      });
    };
  };

  useEffect(() => {
    mountedRef.current = true;
    forceConnect.current = true;

    return () => {
      mountedRef.current = false;
      forceConnect.current = false;
      stopAllIntervals();
      clearTimeouts();
    };
  }, []);

  useEffect(() => {
    const loadProfileId = async (): Promise<void> => {
      try {
        const storedProfileId = await getStorage('userProfileId');
        if (storedProfileId) {
          setProfileId(storedProfileId);
        }
      } catch (error) {}
    };

    loadProfileId();
  }, [getStorage]);

  useEffect(() => {
    if (profileId && !isConnected && !isConnectingRef.current && forceConnect.current) {
      const timeoutId = setTimeout(() => {
        if (!isConnected && !isConnectingRef.current && forceConnect.current) {
          connect();
        }
      }, 100);

      return () => {
        clearTimeout(timeoutId);
      };
    }
  }, [profileId, isConnected]);

  useEffect(() => {
    const appStateSubscription = AppState.addEventListener(
      'change',
      (nextAppState: AppStateStatus) => {
        if (
          nextAppState === 'active' &&
          profileId &&
          !isConnected &&
          !isConnectingRef.current &&
          forceConnect.current
        ) {
          setTimeout(() => {
            if (!isConnected && !isConnectingRef.current && forceConnect.current) {
              connect();
            }
          }, 500);
        }
      },
    );

    return () => {
      appStateSubscription?.remove();
    };
  }, [profileId, isConnected]);

  const value: SocketContextValue = {
    isConnected,
    isConnecting,
    socket,
    sendMessage,
    disconnect,
    subscribeToMessages,
    connectSocket,
    totalUnreadCount,
    setTotalUnreadCount,
  };

  return <SocketContext.Provider value={value}>{children}</SocketContext.Provider>;
};

export const useSocket = (): SocketContextValue => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketContextProvider');
  }
  return context;
};

export default SocketContextProvider;
