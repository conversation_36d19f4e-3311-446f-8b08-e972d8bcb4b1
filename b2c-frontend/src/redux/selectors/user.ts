/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../store';

export const selectUserState = (state: RootState) => state.user;

export const selectAboutState = (state: RootState) => state.about;

export const selectUsername = createSelector(
  [selectUserState],
  (userState) => userState.username || '',
);

export const selectEmail = createSelector([selectUserState], (userState) => userState.email || '');

export const selectProfileId = createSelector(
  [selectUserState],
  (userState) => userState.profileId || '',
);

export const selectAvatar = createSelector(
  [selectUserState],
  (userState) => userState.avatar || null,
);

export const selectToken = createSelector([selectUserState], (userState) => userState.token || '');

export const selectJwtToken = createSelector(
  [selectUserState],
  (userState) => userState.jwtToken || '',
);

export const selectFullName = createSelector(
  [selectUserState],
  (userState) => userState.fullName || '',
);

export const selectGender = createSelector(
  [selectUserState],
  (userState) => userState.gender || '',
);

export const selectCountry = createSelector([selectUserState], (userState) => userState.country);

export const selectOrganisation = createSelector(
  [selectUserState],
  (userState) => userState.organisation,
);

export const selectDesignation = createSelector(
  [selectUserState],
  (userState) => userState.designation,
);

export const selectIsUsernameSaved = createSelector(
  [selectUserState],
  (userState) => userState.isUsernameSaved,
);

export const selectIsEmailVerified = createSelector(
  [selectUserState],
  (userState) => userState.isEmailVerified,
);

export const selectIsPrivacyPolicyAccepted = createSelector(
  [selectUserState],
  (userState) => userState.isPrivacyPolicyAccepted,
);

export const selectIsPersonalDetailsSaved = createSelector(
  [selectUserState],
  (userState) => userState.isPersonalDetailsSaved,
);

export const selectIsWorkDetailsSaved = createSelector(
  [selectUserState],
  (userState) => userState.isWorkDetailsSaved,
);

export const selectIsAuthenticated = createSelector(
  [selectUserState],
  (userState) => userState.isAuthenticated,
);

export const selectLoading = createSelector([selectUserState], (userState) => userState.loading);

export const selectError = createSelector([selectUserState], (userState) => userState.error);

export const selectPendingVerificationEmail = createSelector(
  [selectUserState],
  (userState) => userState.pendingVerificationEmail,
);

export const selectForgotPasswordEmail = createSelector(
  [selectUserState],
  (userState) => userState.forgotPasswordEmail,
);

export const selectIsReferred = createSelector(
  [selectUserState],
  (userState) => userState.isReferred,
);

export const selectIsOTPVerified = createSelector(
  [selectUserState],
  (userState) => userState.isOTPVerified,
);

export const selectPreviousStatus = createSelector(
  [selectUserState],
  (userState) => userState.previousStatus,
);

export const selectReferralCode = createSelector(
  [selectUserState],
  (userState) => userState.referralCode,
);

export const selectReferredByCode = createSelector(
  [selectUserState],
  (userState) => userState.referredByCode,
);

export const selectDescription = createSelector(
  [selectAboutState, selectUserState],
  (aboutState, userState) => aboutState.description || userState.description,
);

export const selectProfileCompletionStatus = createSelector(
  [selectIsUsernameSaved, selectIsPersonalDetailsSaved, selectIsWorkDetailsSaved],
  (isUsernameSaved, isPersonalDetailsSaved, isWorkDetailsSaved) => ({
    isUsernameSaved,
    isPersonalDetailsSaved,
    isWorkDetailsSaved,
    isProfileComplete: isUsernameSaved && isPersonalDetailsSaved && isWorkDetailsSaved,
    completionPercentage:
      (Number(isUsernameSaved) + Number(isPersonalDetailsSaved) + Number(isWorkDetailsSaved)) *
      33.33,
  }),
);

export const selectForgotPasswordState = createSelector(
  [selectForgotPasswordEmail, selectIsOTPVerified, selectLoading, selectError],
  (forgotPasswordEmail, isOTPVerified, loading, error) => ({
    forgotPasswordEmail,
    isOTPVerified,
    loading,
    error,
  }),
);

export const selectCurrentUser = createSelector(
  [
    selectUsername,
    selectEmail,
    selectProfileId,
    selectAvatar,
    selectFullName,
    selectGender,
    selectCountry,
    selectOrganisation,
    selectDesignation,
    selectIsAuthenticated,
    selectIsEmailVerified,
    selectIsUsernameSaved,
    selectIsPrivacyPolicyAccepted,
    selectIsPersonalDetailsSaved,
    selectIsWorkDetailsSaved,
    selectToken,
    selectJwtToken,
    selectDescription,
    selectReferralCode,
    selectReferredByCode,
    selectIsReferred,
  ],
  (
    username,
    email,
    profileId,
    avatar,
    fullName,
    gender,
    country,
    organisation,
    designation,
    isAuthenticated,
    isEmailVerified,
    isUsernameSaved,
    isPrivacyPolicyAccepted,
    isPersonalDetailsSaved,
    isWorkDetailsSaved,
    token,
    jwtToken,
    description,
    referralCode,
    referredByCode,
    isReferred,
  ) => ({
    username,
    email,
    profileId,
    avatar,
    fullName,
    gender,
    country,
    organisation,
    designation,
    isAuthenticated,
    isEmailVerified,
    isUsernameSaved,
    isPrivacyPolicyAccepted,
    isPersonalDetailsSaved,
    isWorkDetailsSaved,
    token,
    jwtToken,
    description,
    referralCode,
    referredByCode,
    isReferred,
  }),
);
