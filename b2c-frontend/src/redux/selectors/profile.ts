/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { RootState } from '../store';

export const selectProfileUi = (state: RootState) => state.profileUi;
export const selectActiveTab = (state: RootState) => state.profileUi.activeTab;
export const selectLastVisitedProfileId = (state: RootState) =>
  state.profileUi.lastVisitedProfileId;
