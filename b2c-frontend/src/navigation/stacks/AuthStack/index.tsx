import React, { useEffect, useState } from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import useStorage from '@/src/hooks/storage';
import { AuthStackParamListI } from '../../types';
import { screens } from './screen';

const AuthStack = createStackNavigator<AuthStackParamListI>();

const AuthStackNavigator = (): React.JSX.Element => {
  const { hasVisitedOnboarding, hasSkippedReferral } = useStorage();
  const [isReady, setIsReady] = useState(false);
  const [initialRoute, setInitialRoute] = useState<keyof AuthStackParamListI>('UserLogin');
  const currentUser = useSelector(selectCurrentUser);
  useEffect(() => {
    const determineInitialRoute = async () => {
      try {
        const visited = await hasVisitedOnboarding();
        const skippedReferral = await hasSkippedReferral();

        if (!visited) {
          setInitialRoute('Onboarding');
          return;
        }

        const {
          token,
          isUsernameSaved,
          isPersonalDetailsSaved,
          isWorkDetailsSaved,
          isEmailVerified,
          isPrivacyPolicyAccepted,
          isReferred,
        } = currentUser;

        if (
          !token ||
          (!isUsernameSaved && !isPersonalDetailsSaved && !isWorkDetailsSaved && !isEmailVerified)
        ) {
          setInitialRoute('UserLogin');
          return;
        }

        if (!isPrivacyPolicyAccepted) {
          setInitialRoute('PolicyAcceptance');
          return;
        }

        if (!isUsernameSaved) {
          if (!isReferred && !skippedReferral) {
            setInitialRoute('ReferralCode');
          } else {
            setInitialRoute('SetUsername');
          }
          return;
        }

        if (!isPersonalDetailsSaved || !isWorkDetailsSaved) {
          setInitialRoute('AddUserDetailScreen');
          return;
        }
      } catch (error) {
        console.error('Failed to determine initial route:', error);
        setInitialRoute('UserLogin');
      } finally {
        setIsReady(true);
      }
    };

    determineInitialRoute();
  }, [currentUser]);

  if (!isReady) {
    return <></>;
  }
  return (
    <AuthStack.Navigator
      screenOptions={{
        headerShown: false,
        animation: 'none',
        cardStyle: { backgroundColor: 'white' },
        cardOverlayEnabled: false,
        cardShadowEnabled: false,
      }}
      initialRouteName={initialRoute}
    >
      {screens.map(({ name, component }) => (
        <AuthStack.Screen key={name} name={name} component={component} />
      ))}
    </AuthStack.Navigator>
  );
};

export default AuthStackNavigator;
