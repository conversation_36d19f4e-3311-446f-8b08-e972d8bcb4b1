import AddUserDetailsScreen from '@/src/screens/AddUserDetail';
import CreateAccountScreen from '@/src/screens/CreateAccount';
import EntitySearchScreen from '@/src/screens/EntitySearch';
import ForgotPasswordScreen from '@/src/screens/ForgotPassword';
import OnboardingScreen from '@/src/screens/Onboarding';
import PolicyAcceptance from '@/src/screens/PolicyAcceptance';
import PrivacyPolicyScreen from '@/src/screens/PrivacyPolicy';
import ReferralCode from '@/src/screens/ReferralCode';
import ResetPasswordScreen from '@/src/screens/ResetPassword';
import SetUsernameScreen from '@/src/screens/SetUsername';
import UserLoginScreen from '@/src/screens/UserLogin';
import VerifyEmail from '@/src/screens/VerifyEmail';
import VerifyPasswordResetScreen from '@/src/screens/VerifyPasswordReset';
import { withErrorBoundary } from '../../../hocs/withErrorBoundary';
import { AuthStackParamListI, StackScreenI } from '../../types';

const OnboardingScreenWithErrorBoundary = withErrorBoundary(OnboardingScreen, {
  title: 'Onboarding Error',
  subtitle: 'Something went wrong during onboarding. Please try again.',
});

const UserLoginScreenWithErrorBoundary = withErrorBoundary(UserLoginScreen, {
  title: 'Login Error',
  subtitle: 'Something went wrong during login. Please try again.',
});

const CreateAccountScreenWithErrorBoundary = withErrorBoundary(CreateAccountScreen, {
  title: 'Account Creation Error',
  subtitle: 'Something went wrong while creating your account. Please try again.',
});

const SetUsernameScreenWithErrorBoundary = withErrorBoundary(SetUsernameScreen, {
  title: 'Username Setup Error',
  subtitle: 'Something went wrong while setting up your username. Please try again.',
});

const AddUserDetailsScreenWithErrorBoundary = withErrorBoundary(AddUserDetailsScreen, {
  title: 'User Details Error',
  subtitle: 'Something went wrong while adding your details. Please try again.',
});

const EntitySearchScreenWithErrorBoundary = withErrorBoundary(EntitySearchScreen, {
  title: 'Search Error',
  subtitle: 'Something went wrong during search. Please try again.',
});

const VerifyEmailScreenWithErrorBoundary = withErrorBoundary(VerifyEmail, {
  title: 'Verify Email Error',
  subtitle: 'Something went wrong during Verify Email. Please try again.',
});

const ForgotPasswordScreenWithErrorBoundary = withErrorBoundary(ForgotPasswordScreen, {
  title: 'Forgot Password Error',
  subtitle: 'Something went wrong during password reset. Please try again.',
});

const VerifyPasswordResetScreenWithErrorBoundary = withErrorBoundary(VerifyPasswordResetScreen, {
  title: 'Verify OTP Error',
  subtitle: 'Something went wrong during OTP verification. Please try again.',
});

const ResetPasswordScreenWithErrorBoundary = withErrorBoundary(ResetPasswordScreen, {
  title: 'Reset Password Error',
  subtitle: 'Something went wrong while resetting password. Please try again.',
});

const PrivacyPolicyScreenWithErrorBoundary = withErrorBoundary(PrivacyPolicyScreen, {
  title: 'Privacy Policy Error',
  subtitle: 'Something went wrong while fetching privacy policy. Please try again.',
});

const PolicyAcceptanceWithErrorBoundary = withErrorBoundary(PolicyAcceptance, {
  title: 'Policy Error',
  subtitle: 'Something went wrong while fetching policies. Please try again.',
});

const ReferralCodeWithErrorBoundary = withErrorBoundary(ReferralCode, {
  title: 'Referral Error',
  subtitle: 'Something went wrong while rendering referral code screen. Please try again.',
});

export const screens: StackScreenI<AuthStackParamListI>[] = [
  { name: 'Onboarding', component: OnboardingScreenWithErrorBoundary },
  { name: 'UserLogin', component: UserLoginScreenWithErrorBoundary },
  { name: 'CreateAccount', component: CreateAccountScreenWithErrorBoundary },
  { name: 'SetUsername', component: SetUsernameScreenWithErrorBoundary },
  { name: 'AddUserDetailScreen', component: AddUserDetailsScreenWithErrorBoundary },
  { name: 'SearchScreen', component: EntitySearchScreenWithErrorBoundary },
  { name: 'VerifyEmail', component: VerifyEmailScreenWithErrorBoundary },
  { name: 'ForgotPassword', component: ForgotPasswordScreenWithErrorBoundary },
  { name: 'VerifyPasswordReset', component: VerifyPasswordResetScreenWithErrorBoundary },
  { name: 'ResetPassword', component: ResetPasswordScreenWithErrorBoundary },
  { name: 'PrivacyPolicy', component: PrivacyPolicyScreenWithErrorBoundary },
  { name: 'PolicyAcceptance', component: PolicyAcceptanceWithErrorBoundary },
  { name: 'ReferralCode', component: ReferralCodeWithErrorBoundary },
];
